import React from 'react';
import { Link } from 'react-router-dom';
import { ScaleIcon } from '@heroicons/react/24/outline';
import { useComparison } from '../../hooks/useComparison';
import { cn } from '../../lib/utils';

const ComparisonFloatingButton = () => {
  const { getComparisonCount } = useComparison();
  const count = getComparisonCount();

  // Don't show if no items in comparison
  if (count === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Link
        to="/comparison"
        className={cn(
          "flex items-center space-x-3 bg-primary-600 text-white px-4 py-3 rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 hover:scale-105",
          "animate-bounce"
        )}
      >
        <div className="relative">
          <ScaleIcon className="w-6 h-6" />
          {count > 0 && (
            <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
              {count}
            </span>
          )}
        </div>
        <span className="font-medium hidden sm:block">
          Bandingkan ({count})
        </span>
      </Link>
    </div>
  );
};

export default ComparisonFloatingButton;
