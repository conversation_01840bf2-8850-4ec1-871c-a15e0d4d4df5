import React, { createContext, useState, useEffect } from 'react';
import { storage } from '../lib/utils';

// Create context
const ComparisonContext = createContext();

// Maximum number of properties that can be compared
const MAX_COMPARISON_ITEMS = 3;

export function ComparisonProvider({ children }) {
  const [comparisonItems, setComparisonItems] = useState([]);

  // Initialize comparison items from localStorage
  useEffect(() => {
    const initializeComparison = () => {
      try {
        const storedItems = storage.get('comparison_items') || [];
        setComparisonItems(storedItems);
      } catch (error) {
        console.error('Error initializing comparison:', error);
        setComparisonItems([]);
      }
    };

    initializeComparison();
  }, []);

  // Save to localStorage whenever comparison items change
  useEffect(() => {
    storage.set('comparison_items', comparisonItems);
  }, [comparisonItems]);

  // Add property to comparison
  const addToComparison = (property) => {
    if (comparisonItems.length >= MAX_COMPARISON_ITEMS) {
      return {
        success: false,
        message: `Maksimal ${MAX_COMPARISON_ITEMS} kost yang dapat dibandingkan`
      };
    }

    if (comparisonItems.find(item => item.id === property.id)) {
      return {
        success: false,
        message: 'Kost sudah ada dalam perbandingan'
      };
    }

    setComparisonItems(prev => [...prev, property]);
    return {
      success: true,
      message: 'Kost berhasil ditambahkan ke perbandingan'
    };
  };

  // Remove property from comparison
  const removeFromComparison = (propertyId) => {
    setComparisonItems(prev => prev.filter(item => item.id !== propertyId));
    return {
      success: true,
      message: 'Kost berhasil dihapus dari perbandingan'
    };
  };

  // Clear all comparison items
  const clearComparison = () => {
    setComparisonItems([]);
    return {
      success: true,
      message: 'Perbandingan berhasil dikosongkan'
    };
  };

  // Check if property is in comparison
  const isInComparison = (propertyId) => {
    return comparisonItems.some(item => item.id === propertyId);
  };

  // Get comparison count
  const getComparisonCount = () => {
    return comparisonItems.length;
  };

  // Check if comparison is full
  const isComparisonFull = () => {
    return comparisonItems.length >= MAX_COMPARISON_ITEMS;
  };

  // Context value
  const value = {
    comparisonItems,
    addToComparison,
    removeFromComparison,
    clearComparison,
    isInComparison,
    getComparisonCount,
    isComparisonFull,
    maxItems: MAX_COMPARISON_ITEMS,
  };

  return (
    <ComparisonContext.Provider value={value}>
      {children}
    </ComparisonContext.Provider>
  );
}



export default ComparisonContext;
