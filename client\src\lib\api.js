import axios from 'axios';
import { storage } from './utils';
import { mockApiResponses } from './mockData';

// Check if we should use mock data (when backend is not available)
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true' || false;

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = storage.get('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401) {
      storage.remove('auth_token');
      storage.remove('user');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

// Helper function to check if backend is available
const checkBackendAvailable = async () => {
  try {
    await api.get('/health', { timeout: 2000 });
    return true;
  } catch {
    return false;
  }
};

// Auth API functions
export const authAPI = {
  register: async (userData) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return await mockApiResponses.register(userData);
    }
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  login: async (credentials) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return await mockApiResponses.login(credentials);
    }
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  logout: async () => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return { success: true, message: 'Logout successful' };
    }
    const response = await api.post('/auth/logout');
    return response.data;
  },

  getProfile: async () => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      const user = storage.get('user');
      return { success: true, data: { user } };
    }
    const response = await api.get('/auth/profile');
    return response.data;
  },

  updateProfile: async (userData) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      const currentUser = storage.get('user');
      const updatedUser = { ...currentUser, ...userData };
      storage.set('user', updatedUser);
      return { success: true, data: { user: updatedUser } };
    }
    const response = await api.put('/auth/profile', userData);
    return response.data;
  },
};

// Kost API functions
export const kostAPI = {
  getProperties: async (params = {}) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return mockApiResponses.getProperties(params);
    }
    const response = await api.get('/kost', { params });
    return response.data;
  },

  getProperty: async (id) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return mockApiResponses.getProperty(id);
    }
    const response = await api.get(`/kost/${id}`);
    return response.data;
  },

  searchProperties: async (searchParams) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return mockApiResponses.getProperties(searchParams);
    }
    const response = await api.get('/kost', { params: searchParams });
    return response.data;
  },
};

// Favorites API functions
export const favoritesAPI = {
  getFavorites: async (params = {}) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return mockApiResponses.getFavorites(params);
    }
    const response = await api.get('/favorites', { params });
    return response.data;
  },

  addFavorite: async (propertyId) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      // Mock add to favorites
      return { success: true, message: 'Property added to favorites' };
    }
    const response = await api.post(`/favorites/${propertyId}`);
    return response.data;
  },

  removeFavorite: async (propertyId) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      // Mock remove from favorites
      return { success: true, message: 'Property removed from favorites' };
    }
    const response = await api.delete(`/favorites/${propertyId}`);
    return response.data;
  },

  checkFavorite: async (propertyId) => {
    const backendAvailable = await checkBackendAvailable();
    if (!backendAvailable) {
      return { success: true, data: { is_favorited: false } };
    }
    const response = await api.get(`/favorites/check/${propertyId}`);
    return response.data;
  },
};

// Generic API error handler
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    
    switch (status) {
      case 400:
        return {
          message: data.message || 'Bad request',
          errors: data.errors || [],
        };
      case 401:
        return {
          message: 'Unauthorized. Please login again.',
          errors: [],
        };
      case 403:
        return {
          message: 'Access forbidden',
          errors: [],
        };
      case 404:
        return {
          message: 'Resource not found',
          errors: [],
        };
      case 422:
        return {
          message: data.message || 'Validation failed',
          errors: data.errors || [],
        };
      case 429:
        return {
          message: 'Too many requests. Please try again later.',
          errors: [],
        };
      case 500:
        return {
          message: 'Internal server error. Please try again later.',
          errors: [],
        };
      default:
        return {
          message: data.message || 'An error occurred',
          errors: data.errors || [],
        };
    }
  } else if (error.request) {
    // Network error
    return {
      message: 'Network error. Please check your connection.',
      errors: [],
    };
  } else {
    // Other error
    return {
      message: error.message || 'An unexpected error occurred',
      errors: [],
    };
  }
};

// Upload helper (for future use)
export const uploadAPI = {
  uploadImage: async (file, onProgress) => {
    const formData = new FormData();
    formData.append('image', file);

    const response = await api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          onProgress(percentCompleted);
        }
      },
    });

    return response.data;
  },
};

export default api;
