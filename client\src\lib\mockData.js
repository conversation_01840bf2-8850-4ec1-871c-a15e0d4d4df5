// Mock data for development and testing
export const mockProperties = [
  {
    id: '1',
    name: 'Kost Melati Indah',
    description: 'Kost nyaman dan strategis dekat kampus dan pusat kota. Fasilitas lengkap dengan keamanan 24 jam.',
    address: 'Jl. Melati No. 15',
    city: 'Malang',
    province: 'Jawa Timur',
    price_min: 800000,
    price_max: 1200000,
    total_rooms: 20,
    available_rooms: 15,
    property_type: 'kost',
    gender_type: 'mixed',
    main_photo_url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
    is_featured: true,
    view_count: 245,
    is_favorited: false,
    facilities: [
      { name: 'WiFi', icon: 'wifi', category: 'both' },
      { name: 'Parking', icon: 'car', category: 'property' },
      { name: 'Security 24/7', icon: 'shield', category: 'property' },
      { name: 'AC', icon: 'snowflake', category: 'room' },
      { name: '<PERSON><PERSON><PERSON>', icon: 'washing-machine', category: 'property' }
    ],
    owner: {
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+62 812-3456-7890'
    },
    photos: [
      { url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800', caption: 'Tampak depan' },
      { url: 'https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=800', caption: 'Kamar tidur' },
      { url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800', caption: 'Kamar mandi' },
      { url: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?w=800', caption: 'Dapur bersama' }
    ],
    rooms: [
      {
        id: 'r1',
        room_number: 'A1',
        room_type: 'single',
        price: 800000,
        size_sqm: 12.0,
        is_available: true,
        max_occupancy: 1,
        facilities: [
          { name: 'AC', icon: 'snowflake' },
          { name: 'Private Bathroom', icon: 'bath' },
          { name: 'Wardrobe', icon: 'cabinet' }
        ]
      },
      {
        id: 'r2',
        room_number: 'A2',
        room_type: 'shared',
        price: 1000000,
        size_sqm: 15.0,
        is_available: true,
        max_occupancy: 2,
        facilities: [
          { name: 'AC', icon: 'snowflake' },
          { name: 'Private Bathroom', icon: 'bath' },
          { name: 'Balcony', icon: 'balcony' }
        ]
      }
    ],
    testimonials: [
      {
        reviewer_name: 'Andi Pratama',
        rating: 5,
        review_text: 'Kost yang sangat nyaman dan bersih. Pemilik ramah dan fasilitas lengkap. Recommended!',
        stay_duration: '8 bulan',
        created_at: '2024-01-15T00:00:00Z'
      },
      {
        reviewer_name: 'Sari Dewi',
        rating: 4,
        review_text: 'Lokasi strategis dan aman. Cocok untuk mahasiswi. Hanya saja kadang WiFi agak lambat.',
        stay_duration: '6 bulan',
        created_at: '2024-02-20T00:00:00Z'
      }
    ],
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Kost Mawar Residence',
    description: 'Kost eksklusif dengan fasilitas premium. Cocok untuk mahasiswa dan pekerja muda.',
    address: 'Jl. Mawar Raya No. 8',
    city: 'Malang',
    province: 'Jawa Timur',
    price_min: 1000000,
    price_max: 1500000,
    total_rooms: 15,
    available_rooms: 10,
    property_type: 'kost',
    gender_type: 'female',
    main_photo_url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
    is_featured: true,
    view_count: 189,
    is_favorited: false,
    facilities: [
      { name: 'WiFi', icon: 'wifi', category: 'both' },
      { name: 'Parking', icon: 'car', category: 'property' },
      { name: 'Security 24/7', icon: 'shield', category: 'property' },
      { name: 'Swimming Pool', icon: 'waves', category: 'property' },
      { name: 'Gym', icon: 'dumbbell', category: 'property' }
    ],
    owner: {
      name: 'Sari Dewi',
      email: '<EMAIL>',
      phone: '+62 813-4567-8901'
    },
    photos: [
      { url: 'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800', caption: 'Tampak depan' },
      { url: 'https://images.unsplash.com/photo-1631049307264-da0ec9d70304?w=800', caption: 'Kolam renang' },
      { url: 'https://images.unsplash.com/photo-1571624436279-b272aff752b5?w=800', caption: 'Gym' }
    ],
    rooms: [],
    testimonials: [
      {
        reviewer_name: 'Maya Sari',
        rating: 5,
        review_text: 'Fasilitas lengkap dan modern. Kolam renang dan gym sangat membantu untuk olahraga.',
        stay_duration: '1 tahun',
        created_at: '2024-03-10T00:00:00Z'
      }
    ],
    created_at: '2024-01-15T00:00:00Z'
  },
  {
    id: '3',
    name: 'Kost Anggrek Modern',
    description: 'Kost modern dengan desain minimalis. Dekat dengan berbagai fasilitas umum.',
    address: 'Jl. Anggrek No. 22',
    city: 'Yogyakarta',
    province: 'DI Yogyakarta',
    price_min: 700000,
    price_max: 1000000,
    total_rooms: 25,
    available_rooms: 18,
    property_type: 'kost',
    gender_type: 'male',
    main_photo_url: 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800',
    is_featured: false,
    view_count: 156,
    is_favorited: false,
    facilities: [
      { name: 'WiFi', icon: 'wifi', category: 'both' },
      { name: 'Parking', icon: 'car', category: 'property' },
      { name: 'Kitchen', icon: 'chef-hat', category: 'property' },
      { name: 'CCTV', icon: 'camera', category: 'property' }
    ],
    owner: {
      name: 'Ahmad Rahman',
      email: '<EMAIL>',
      phone: '+62 814-5678-9012'
    },
    photos: [
      { url: 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800', caption: 'Tampak depan' }
    ],
    rooms: [],
    testimonials: [],
    created_at: '2024-02-01T00:00:00Z'
  }
];

export const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  full_name: 'John Doe',
  phone: '+62 812-3456-7890',
  role: 'user',
  email_verified: true,
  created_at: '2024-01-01T00:00:00Z'
};

export const mockFavorites = [
  mockProperties[0],
  mockProperties[1]
];

// Mock API responses
export const mockApiResponses = {
  getProperties: (params = {}) => {
    let filteredProperties = [...mockProperties];
    
    // Apply filters
    if (params.city) {
      filteredProperties = filteredProperties.filter(p => 
        p.city.toLowerCase().includes(params.city.toLowerCase())
      );
    }
    
    if (params.search) {
      filteredProperties = filteredProperties.filter(p => 
        p.name.toLowerCase().includes(params.search.toLowerCase()) ||
        p.description.toLowerCase().includes(params.search.toLowerCase())
      );
    }
    
    if (params.price_min) {
      filteredProperties = filteredProperties.filter(p => 
        p.price_min >= parseInt(params.price_min)
      );
    }
    
    if (params.price_max) {
      filteredProperties = filteredProperties.filter(p => 
        p.price_max <= parseInt(params.price_max)
      );
    }
    
    if (params.gender_type && params.gender_type !== 'all') {
      filteredProperties = filteredProperties.filter(p => 
        p.gender_type === params.gender_type
      );
    }
    
    // Pagination
    const page = parseInt(params.page) || 1;
    const limit = parseInt(params.limit) || 12;
    const offset = (page - 1) * limit;
    const paginatedProperties = filteredProperties.slice(offset, offset + limit);
    
    return {
      success: true,
      data: {
        properties: paginatedProperties,
        pagination: {
          current_page: page,
          total_pages: Math.ceil(filteredProperties.length / limit),
          total_items: filteredProperties.length,
          items_per_page: limit,
          has_next: page < Math.ceil(filteredProperties.length / limit),
          has_prev: page > 1
        }
      }
    };
  },
  
  getProperty: (id) => {
    const property = mockProperties.find(p => p.id === id);
    if (!property) {
      throw new Error('Property not found');
    }
    
    return {
      success: true,
      data: property
    };
  },
  
  login: (credentials) => {
    // Mock login - accept any email/password for demo
    // Log credentials for development debugging
    console.log('Mock login attempt:', { email: credentials?.email });

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            user: mockUser,
            token: 'mock-jwt-token-' + Date.now()
          }
        });
      }, 1000);
    });
  },
  
  register: (userData) => {
    // Mock registration
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            user: { ...mockUser, ...userData },
            token: 'mock-jwt-token-' + Date.now()
          }
        });
      }, 1500);
    });
  },
  
  getFavorites: () => {
    return {
      success: true,
      data: {
        properties: mockFavorites,
        pagination: {
          current_page: 1,
          total_pages: 1,
          total_items: mockFavorites.length,
          items_per_page: 12,
          has_next: false,
          has_prev: false
        }
      }
    };
  }
};
