-- Seed data for Kost Platform
-- Run this after schema.sql

-- Insert facilities
INSERT INTO facilities (name, category, icon, description) VALUES
-- Property facilities
('WiFi', 'both', 'wifi', 'Internet connection available'),
('Parking', 'property', 'car', 'Vehicle parking space'),
('Security 24/7', 'property', 'shield', '24-hour security service'),
('CCTV', 'property', 'camera', 'Security camera monitoring'),
('Laundry', 'property', 'washing-machine', 'Laundry service or facilities'),
('Kitchen', 'property', 'chef-hat', 'Shared kitchen facilities'),
('Living Room', 'property', 'sofa', 'Common living area'),
('Garden', 'property', 'tree', 'Garden or outdoor space'),
('Gym', 'property', 'dumbbell', 'Fitness facilities'),
('Swimming Pool', 'property', 'waves', 'Swimming pool'),

-- Room facilities
('AC', 'room', 'snowflake', 'Air conditioning'),
('Private Bathroom', 'room', 'bath', 'Private bathroom in room'),
('Wardrobe', 'room', 'cabinet', 'Built-in wardrobe'),
('Desk & Chair', 'room', 'desk', 'Study desk and chair'),
('Bed', 'room', 'bed', 'Bed provided'),
('TV', 'room', 'tv', 'Television in room'),
('Refrigerator', 'room', 'refrigerator', 'Mini fridge in room'),
('Balcony', 'room', 'balcony', 'Private balcony'),
('Window', 'room', 'window', 'Natural lighting'),
('Fan', 'room', 'fan', 'Ceiling or wall fan');

-- Insert sample users
INSERT INTO users (email, password_hash, full_name, phone, role, email_verified) VALUES
('<EMAIL>', '$2b$10$example_hash_admin', 'Admin Kost', '081234567890', 'admin', true),
('<EMAIL>', '$2b$10$example_hash_owner1', 'Budi Santoso', '081234567891', 'owner', true),
('<EMAIL>', '$2b$10$example_hash_owner2', 'Sari Dewi', '081234567892', 'owner', true),
('<EMAIL>', '$2b$10$example_hash_user1', 'Ahmad Rahman', '081234567893', 'user', true),
('<EMAIL>', '$2b$10$example_hash_user2', 'Maya Sari', '081234567894', 'user', true);

-- Get user IDs for reference (you'll need to adjust these UUIDs based on actual generated ones)
-- For demo purposes, we'll use the emails to reference in subsequent inserts

-- Insert sample kost properties
INSERT INTO kost_properties (
    owner_id, name, description, address, city, province, 
    price_min, price_max, total_rooms, available_rooms, 
    gender_type, main_photo_url, is_featured
) VALUES
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'Kost Melati Indah',
    'Kost nyaman dan strategis dekat kampus dan pusat kota. Fasilitas lengkap dengan keamanan 24 jam.',
    'Jl. Melati No. 15, Malang',
    'Malang',
    'Jawa Timur',
    800000, 1200000, 20, 15, 'mixed',
    'https://example.com/photos/melati-main.jpg',
    true
),
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'Kost Mawar Residence',
    'Kost eksklusif dengan fasilitas premium. Cocok untuk mahasiswa dan pekerja muda.',
    'Jl. Mawar Raya No. 8, Malang',
    'Malang',
    'Jawa Timur',
    1000000, 1500000, 15, 10, 'female',
    'https://example.com/photos/mawar-main.jpg',
    true
),
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'Kost Anggrek Modern',
    'Kost modern dengan desain minimalis. Dekat dengan berbagai fasilitas umum.',
    'Jl. Anggrek No. 22, Yogyakarta',
    'Yogyakarta',
    'DI Yogyakarta',
    700000, 1000000, 25, 18, 'male',
    'https://example.com/photos/anggrek-main.jpg',
    false
),
(
    (SELECT id FROM users WHERE email = '<EMAIL>'),
    'Kost Sakura House',
    'Kost dengan suasana homey dan nyaman. Fasilitas lengkap dan harga terjangkau.',
    'Jl. Sakura No. 5, Yogyakarta',
    'Yogyakarta',
    'DI Yogyakarta',
    600000, 900000, 18, 12, 'mixed',
    'https://example.com/photos/sakura-main.jpg',
    false
);

-- Insert property facilities
INSERT INTO property_facilities (property_id, facility_id, is_available) 
SELECT 
    kp.id,
    f.id,
    true
FROM kost_properties kp
CROSS JOIN facilities f
WHERE kp.name = 'Kost Melati Indah' 
AND f.name IN ('WiFi', 'Parking', 'Security 24/7', 'CCTV', 'Laundry', 'Kitchen', 'Living Room');

INSERT INTO property_facilities (property_id, facility_id, is_available) 
SELECT 
    kp.id,
    f.id,
    true
FROM kost_properties kp
CROSS JOIN facilities f
WHERE kp.name = 'Kost Mawar Residence' 
AND f.name IN ('WiFi', 'Parking', 'Security 24/7', 'CCTV', 'Laundry', 'Kitchen', 'Living Room', 'Gym', 'Swimming Pool');

INSERT INTO property_facilities (property_id, facility_id, is_available) 
SELECT 
    kp.id,
    f.id,
    true
FROM kost_properties kp
CROSS JOIN facilities f
WHERE kp.name = 'Kost Anggrek Modern' 
AND f.name IN ('WiFi', 'Parking', 'Security 24/7', 'CCTV', 'Laundry', 'Kitchen');

INSERT INTO property_facilities (property_id, facility_id, is_available) 
SELECT 
    kp.id,
    f.id,
    true
FROM kost_properties kp
CROSS JOIN facilities f
WHERE kp.name = 'Kost Sakura House' 
AND f.name IN ('WiFi', 'Parking', 'Security 24/7', 'Laundry', 'Kitchen', 'Living Room', 'Garden');

-- Insert sample rooms
INSERT INTO rooms (property_id, room_number, room_type, price, size_sqm, is_available, is_furnished, max_occupancy, description)
SELECT 
    kp.id,
    'A' || generate_series(1, 10),
    CASE 
        WHEN generate_series(1, 10) <= 6 THEN 'single'
        WHEN generate_series(1, 10) <= 8 THEN 'shared'
        ELSE 'suite'
    END,
    CASE 
        WHEN generate_series(1, 10) <= 6 THEN 800000
        WHEN generate_series(1, 10) <= 8 THEN 1000000
        ELSE 1200000
    END,
    CASE 
        WHEN generate_series(1, 10) <= 6 THEN 12.0
        WHEN generate_series(1, 10) <= 8 THEN 15.0
        ELSE 20.0
    END,
    CASE WHEN generate_series(1, 10) <= 8 THEN true ELSE false END,
    true,
    CASE 
        WHEN generate_series(1, 10) <= 6 THEN 1
        WHEN generate_series(1, 10) <= 8 THEN 2
        ELSE 1
    END,
    'Kamar nyaman dengan fasilitas lengkap'
FROM kost_properties kp
WHERE kp.name = 'Kost Melati Indah';

-- Insert sample testimonials
INSERT INTO testimonials (property_id, reviewer_name, rating, review_text, stay_duration, is_verified)
SELECT 
    kp.id,
    'Andi Pratama',
    5,
    'Kost yang sangat nyaman dan bersih. Pemilik ramah dan fasilitas lengkap. Recommended!',
    '8 bulan',
    true
FROM kost_properties kp
WHERE kp.name = 'Kost Melati Indah';

INSERT INTO testimonials (property_id, reviewer_name, rating, review_text, stay_duration, is_verified)
SELECT 
    kp.id,
    'Sinta Dewi',
    4,
    'Lokasi strategis dan aman. Cocok untuk mahasiswi. Hanya saja kadang WiFi agak lambat.',
    '1 tahun',
    true
FROM kost_properties kp
WHERE kp.name = 'Kost Mawar Residence';

-- Update room counts in properties
UPDATE kost_properties SET 
    total_rooms = (SELECT COUNT(*) FROM rooms WHERE property_id = kost_properties.id),
    available_rooms = (SELECT COUNT(*) FROM rooms WHERE property_id = kost_properties.id AND is_available = true);
