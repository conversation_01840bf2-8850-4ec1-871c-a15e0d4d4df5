-- Drop all existing tables and functions
-- Run this to clean up the database before running schema.sql

-- Drop all tables in correct order (respecting foreign key dependencies)
DROP TABLE IF EXISTS property_views CASCADE;
DROP TABLE IF EXISTS testimonials CASCADE;
DROP TABLE IF EXISTS favorites CASCADE;
DROP TABLE IF EXISTS photos CASCADE;
DROP TABLE IF EXISTS room_facilities CASCADE;
DROP TABLE IF EXISTS property_facilities CASCADE;
DROP TABLE IF EXISTS rooms CASCADE;
DROP TABLE IF EXISTS facilities CASCADE;
DROP TABLE IF EXISTS kost_properties CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Drop functions if they exist
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Drop indexes if they exist (they should be dropped with tables, but just in case)
DROP INDEX IF EXISTS idx_kost_properties_city;
DROP INDEX IF EXISTS idx_kost_properties_price;
DROP INDEX IF EXISTS idx_kost_properties_active;
DROP INDEX IF EXISTS idx_kost_properties_featured;
DROP INDEX IF EXISTS idx_rooms_property;
DROP INDEX IF EXISTS idx_rooms_available;
DROP INDEX IF EXISTS idx_photos_property;
DROP INDEX IF EXISTS idx_photos_room;
DROP INDEX IF EXISTS idx_favorites_user;
DROP INDEX IF EXISTS idx_testimonials_property;
DROP INDEX IF EXISTS idx_property_views_property;

-- Verify cleanup
SELECT 'Tables remaining:' as status;
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;
