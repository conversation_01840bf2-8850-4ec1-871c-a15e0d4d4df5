-- Fix rooms data and add missing data
-- Run this after seed_data.sql to fix the rooms insertion error

-- Insert sample rooms for Kost Melati Indah
INSERT INTO rooms (property_id, room_number, room_type, price, size_sqm, is_available, is_furnished, max_occupancy, description)
SELECT 
    kp.id,
    'A' || num,
    CASE 
        WHEN num <= 6 THEN 'single'
        WHEN num <= 8 THEN 'shared'
        ELSE 'suite'
    END,
    CASE 
        WHEN num <= 6 THEN 800000
        WHEN num <= 8 THEN 1000000
        ELSE 1200000
    END,
    CASE 
        WHEN num <= 6 THEN 12.0
        WHEN num <= 8 THEN 15.0
        ELSE 20.0
    END,
    CASE WHEN num <= 8 THEN true ELSE false END,
    true,
    CASE 
        WHEN num <= 6 THEN 1
        WHEN num <= 8 THEN 2
        ELSE 1
    END,
    '<PERSON><PERSON> nyaman dengan fasilitas lengkap'
FROM kost_properties kp
CROSS JOIN generate_series(1, 10) AS num
WHERE kp.name = 'Kost Melati Indah';

-- Insert sample rooms for other properties
INSERT INTO rooms (property_id, room_number, room_type, price, size_sqm, is_available, is_furnished, max_occupancy, description)
SELECT 
    kp.id,
    'B' || num,
    CASE 
        WHEN num <= 5 THEN 'single'
        WHEN num <= 7 THEN 'shared'
        ELSE 'suite'
    END,
    CASE 
        WHEN num <= 5 THEN 1000000
        WHEN num <= 7 THEN 1200000
        ELSE 1500000
    END,
    CASE 
        WHEN num <= 5 THEN 14.0
        WHEN num <= 7 THEN 18.0
        ELSE 25.0
    END,
    CASE WHEN num <= 7 THEN true ELSE false END,
    true,
    CASE 
        WHEN num <= 5 THEN 1
        WHEN num <= 7 THEN 2
        ELSE 1
    END,
    'Kamar premium dengan fasilitas mewah'
FROM kost_properties kp
CROSS JOIN generate_series(1, 8) AS num
WHERE kp.name = 'Kost Mawar Residence';

-- Insert rooms for Kost Anggrek Modern
INSERT INTO rooms (property_id, room_number, room_type, price, size_sqm, is_available, is_furnished, max_occupancy, description)
SELECT 
    kp.id,
    'C' || num,
    CASE 
        WHEN num <= 8 THEN 'single'
        ELSE 'shared'
    END,
    CASE 
        WHEN num <= 8 THEN 700000
        ELSE 1000000
    END,
    CASE 
        WHEN num <= 8 THEN 12.0
        ELSE 16.0
    END,
    true,
    true,
    CASE 
        WHEN num <= 8 THEN 1
        ELSE 2
    END,
    'Kamar modern dengan desain minimalis'
FROM kost_properties kp
CROSS JOIN generate_series(1, 12) AS num
WHERE kp.name = 'Kost Anggrek Modern';

-- Insert rooms for Kost Sakura House
INSERT INTO rooms (property_id, room_number, room_type, price, size_sqm, is_available, is_furnished, max_occupancy, description)
SELECT 
    kp.id,
    'D' || num,
    CASE 
        WHEN num <= 6 THEN 'single'
        ELSE 'shared'
    END,
    CASE 
        WHEN num <= 6 THEN 600000
        ELSE 900000
    END,
    CASE 
        WHEN num <= 6 THEN 10.0
        ELSE 14.0
    END,
    true,
    true,
    CASE 
        WHEN num <= 6 THEN 1
        ELSE 2
    END,
    'Kamar homey dengan suasana nyaman'
FROM kost_properties kp
CROSS JOIN generate_series(1, 10) AS num
WHERE kp.name = 'Kost Sakura House';

-- Insert room facilities for some rooms
INSERT INTO room_facilities (room_id, facility_id, is_available)
SELECT 
    r.id,
    f.id,
    true
FROM rooms r
CROSS JOIN facilities f
WHERE r.room_type = 'suite' 
AND f.name IN ('AC', 'Private Bathroom', 'Wardrobe', 'Desk & Chair', 'Bed', 'TV', 'Refrigerator');

INSERT INTO room_facilities (room_id, facility_id, is_available)
SELECT 
    r.id,
    f.id,
    true
FROM rooms r
CROSS JOIN facilities f
WHERE r.room_type = 'single' 
AND f.name IN ('AC', 'Wardrobe', 'Desk & Chair', 'Bed', 'Window');

INSERT INTO room_facilities (room_id, facility_id, is_available)
SELECT 
    r.id,
    f.id,
    true
FROM rooms r
CROSS JOIN facilities f
WHERE r.room_type = 'shared' 
AND f.name IN ('AC', 'Wardrobe', 'Desk & Chair', 'Bed', 'Window', 'Fan');

-- Insert sample photos
INSERT INTO photos (property_id, url, caption, is_main, sort_order) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Indah'), 'https://example.com/photos/melati-main.jpg', 'Tampak depan Kost Melati Indah', true, 1),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Indah'), 'https://example.com/photos/melati-room.jpg', 'Contoh kamar single', false, 2),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Indah'), 'https://example.com/photos/melati-kitchen.jpg', 'Dapur bersama', false, 3),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Residence'), 'https://example.com/photos/mawar-main.jpg', 'Tampak depan Kost Mawar Residence', true, 1),
((SELECT id FROM kost_properties WHERE name = 'Kost Mawar Residence'), 'https://example.com/photos/mawar-pool.jpg', 'Kolam renang', false, 2),
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Modern'), 'https://example.com/photos/anggrek-main.jpg', 'Tampak depan Kost Anggrek Modern', true, 1),
((SELECT id FROM kost_properties WHERE name = 'Kost Sakura House'), 'https://example.com/photos/sakura-main.jpg', 'Tampak depan Kost Sakura House', true, 1);

-- Add more testimonials
INSERT INTO testimonials (property_id, reviewer_name, rating, review_text, stay_duration, is_verified) VALUES
((SELECT id FROM kost_properties WHERE name = 'Kost Anggrek Modern'), 'Rudi Hartono', 4, 'Kost modern dengan fasilitas bagus. Lokasi strategis dekat kampus.', '6 bulan', true),
((SELECT id FROM kost_properties WHERE name = 'Kost Sakura House'), 'Lisa Permata', 5, 'Suasana homey dan nyaman. Pemilik sangat ramah dan helpful.', '1 tahun', true),
((SELECT id FROM kost_properties WHERE name = 'Kost Melati Indah'), 'Doni Setiawan', 4, 'Fasilitas lengkap dan harga reasonable. Recommended untuk mahasiswa.', '4 bulan', true);

-- Update room counts in properties
UPDATE kost_properties SET 
    total_rooms = (SELECT COUNT(*) FROM rooms WHERE property_id = kost_properties.id),
    available_rooms = (SELECT COUNT(*) FROM rooms WHERE property_id = kost_properties.id AND is_available = true);

-- Verify the data
SELECT 'Data verification:' as status;
SELECT 'Users:' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'Properties:', COUNT(*) FROM kost_properties
UNION ALL
SELECT 'Facilities:', COUNT(*) FROM facilities
UNION ALL
SELECT 'Rooms:', COUNT(*) FROM rooms
UNION ALL
SELECT 'Property Facilities:', COUNT(*) FROM property_facilities
UNION ALL
SELECT 'Room Facilities:', COUNT(*) FROM room_facilities
UNION ALL
SELECT 'Photos:', COUNT(*) FROM photos
UNION ALL
SELECT 'Testimonials:', COUNT(*) FROM testimonials;
